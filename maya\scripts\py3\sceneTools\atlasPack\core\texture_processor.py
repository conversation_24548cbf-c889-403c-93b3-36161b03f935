import os
import traceback
from sceneTools.atlasPack.core.maya_utils import *


def collect_texture_data(selected_objects):
	"""
	Accepts a list of mesh or material names.
	Returns a list of dictionaries with keys: material_name, texture_name, texture_path, width, height.
	"""
	texture_data = []
	for obj in selected_objects:

		materials = [obj]
		for mat in materials:
			texture_path = get_texture_file_from_material_color(mat)
			if texture_path:
				texture_name = os.path.basename(texture_path)
				width, height = get_image_dimensions(texture_path) or (None, None)
				texture_data.append({
					"material_name": mat,
					"texture_name": texture_name,
					"texture_path": texture_path,
					"width": width,
					"height": height
				})
	return texture_data


def main():
	"""
	Main function to execute the script.
	"""
	shading_groups = get_sg_from_selection()
	print("Shading groups for processing:", shading_groups)
	if not shading_groups:
		print("No SG or mesh with SG selected.")
		return
	for sg in shading_groups:
		meshes = get_meshes_with_sg(sg)
		print("Meshes with shading group", sg, ":", meshes)
		for mesh in meshes:
			store_materials_attrs(mesh)
			restore_materials_attrs(mesh)


# texture_data = collect_texture_data(shading_groups)
# for data in texture_data:
# 	print(data)
# create_proxy_mesh(texture_data)

def reload_all_modules(module_name):
	print('loaded {}'.format(module_name))
	for m in list(sys.modules):
		if module_name in m:
			print('deleted {}'.format(m))
			del (sys.modules[m])


if __name__ == "__main__":  # pragma: no cover
	reload_all_modules('atlasPack')
	main()
