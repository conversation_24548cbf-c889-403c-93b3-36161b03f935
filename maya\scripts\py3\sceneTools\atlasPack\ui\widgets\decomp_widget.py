from pyside_compat import *
from .filtered_combobox import FilteredComboBox


class DecompositionGroupBox(QGroupBox):
	"""
	A group box widget for Atlas Decomposition functionality.
	Contains controls for drawing frames, selecting atlas, and decomposing atlas.
	"""

	def __init__(self, processor=None, parent=None):
		super(DecompositionGroupBox, self).__init__("Atlas Decomposition", parent)

		self.processor = processor
		self.parent_window = parent

		# Setup the group box properties
		self.setCheckable(True)  # Make group collapsible
		self.setChecked(False)  # Collapsed by default

		# Initialize UI components
		self._setup_ui()
		self._connect_signals()

		# Populate atlas materials if processor is available
		if self.processor:
			self._populate_atlas_materials()

	def _setup_ui(self):
		"""Setup the UI layout and widgets"""
		group_internal_layout = QVBoxLayout()
		group_internal_layout.setContentsMargins(6, 6, 6, 6)  # Отступы для содержимого внутри группы
		self.setLayout(group_internal_layout)
		self.collapsible_atlas_content_widget = QWidget()

		self.edit_layout = QFormLayout(self.collapsible_atlas_content_widget)
		self.edit_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
		self.edit_layout.setContentsMargins(0, 0, 0, 0)

		# Draw Frame button
		self.draw_rect_button = QPushButton("Draw Frame")
		self.draw_rect_button.setToolTip(
			"Toggle rectangle drawing mode. Left click to draw, right click to select/delete.")
		self.draw_rect_button.setCheckable(True)  # Make button toggleable

		self.edit_layout.addRow(self.draw_rect_button)

		# Atlas selection layout
		self.atlas_name_layout = QHBoxLayout()
		self.output_atlasname_line_edit = FilteredComboBox()
		self.output_atlasname_line_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
		self.output_atlasname_line_edit.setMinimumHeight(22)
		self.output_atlasname_line_edit.setToolTip("Select existing atlas to decompose.")

		# Create invisible spacer widget for alignment (same width as Browse button)
		self.atlasname_spacer = QWidget(self)
		self.atlasname_spacer.setFixedWidth(60)
		self.atlasname_spacer.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)

		# Add combobox to layout to occupy same space as directory field
		self.atlas_name_layout.addWidget(self.output_atlasname_line_edit)
		self.atlas_name_layout.addWidget(self.atlasname_spacer)
		self.edit_layout.addRow("Atlas:", self.atlas_name_layout)

		# Decompose button
		self.decompose_button = QPushButton("Decompose")
		self.decompose_button.setToolTip("Split atlas into individual images.")
		self.edit_layout.addRow(self.decompose_button)

		group_internal_layout.addWidget(self.collapsible_atlas_content_widget)
		self.toggled.connect(self.collapsible_atlas_content_widget.setVisible)
		self.collapsible_atlas_content_widget.setVisible(self.isChecked())

	# self.main_layout.addWidget(self.edit_group_box)

	def _connect_signals(self):
		"""Connect UI signals to their respective slots"""
		self.draw_rect_button.clicked.connect(self._on_draw_rect_clicked)
		self.output_atlasname_line_edit.currentIndexChanged.connect(self._on_atlas_selection_changed)
		self.decompose_button.clicked.connect(self._on_decompose_clicked)

	def _populate_atlas_materials(self):
		"""Populate the atlas materials dropdown"""
		if self.processor:
			atlas_materials = self.processor.update_atlas_list_materials()
			self.output_atlasname_line_edit.populate_items(atlas_materials)

	def _on_draw_rect_clicked(self):
		"""Handle Draw Frame button click"""
		if self.parent_window and hasattr(self.parent_window, '_on_draw_rect_clicked'):
			self.parent_window._on_draw_rect_clicked()

	def _on_atlas_selection_changed(self):
		"""Handle atlas selection change"""
		if self.parent_window and hasattr(self.parent_window, '_update_ui_state'):
			self.parent_window._update_ui_state()

	def _on_decompose_clicked(self):
		"""Handle Decompose button click"""
		if self.parent_window and hasattr(self.parent_window, '_on_decompose_clicked'):
			self.parent_window._on_decompose_clicked()
		else:
			print("Decompose functionality not implemented yet")

	def get_selected_atlas(self):
		"""Get the currently selected atlas name"""
		return self.output_atlasname_line_edit.currentText()

	def is_draw_mode_active(self):
		"""Check if draw mode is active"""
		return self.draw_rect_button.isChecked()

	def set_draw_mode(self, active):
		"""Set the draw mode state"""
		self.draw_rect_button.setChecked(active)

	def get_atlas_selection_index(self):
		"""Get the current atlas selection index"""
		return self.output_atlasname_line_edit.currentIndex()

	def update_decompose_button_state(self, enabled):
		"""Update the enabled state of the decompose button"""
		self.decompose_button.setEnabled(enabled)
