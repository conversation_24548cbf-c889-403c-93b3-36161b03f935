from pyside_compat import *
import traceback
import os


class PreviewFrame(QFrame):
	def __init__(self, parent=None, processor=None):
		super(PreviewFrame, self).__init__(parent)
		self.processor = processor
		self.parent = parent

		self.texture_bake_position = []
		self.setStyleSheet("background-color: gray;")
		self.setMinimumSize(300, 300)  # Set minimum size for the preview frame

		self._buffer = None  # Off-screen buffer for drawing

		''' This buffer is used to draw the preview content off-screen'''

		# Добавляем переменные для рисования прямоугольников
		self.draw_mode = False
		self.rectangles = []  # Список прямоугольников [(start_pos, end_pos, color), ...]
		self.current_rect = None  # Текущий рисуемый прямоугольник
		self.selected_rect_index = -1  # Индекс выбранного прямоугольника

		# Включаем отслеживание мыши
		self.setMouseTracking(True)
		'''		Initialize the preview frame with a gray background and minimum size.'''

	def toggle_draw_mode(self):
		"""Переключает режим рисования прямоугольников"""
		self.draw_mode = not self.draw_mode
		if self.draw_mode:
			# Создаем белый крестообразный курсор
			pixmap = QPixmap(16, 16)
			pixmap.fill(Qt.transparent)
			painter = QPainter(pixmap)
			painter.setPen(QPen(Qt.white, 1))
			painter.drawLine(8, 0, 8, 16)  # Вертикальная линия
			painter.drawLine(0, 8, 16, 8)  # Горизонтальная линия
			painter.end()
			self.setCursor(QCursor(pixmap, 8, 8))  # Центр курсора в точке пересечения линий
		else:
			self.setCursor(Qt.ArrowCursor)
		self.selected_rect_index = -1  # Сбрасываем выбор при переключении режима
		self.update()

	def mousePressEvent(self, event):
		if not self.draw_mode:
			return

		if event.button() == Qt.LeftButton:
			if self.current_rect is None:
				# Начинаем рисовать новый прямоугольник
				pos = self._clamp_position(event.pos())
				self.current_rect = (pos, pos, QColor(255, 0, 0, 100))  # Полупрозрачный красный
				self.selected_rect_index = -1  # Сбрасываем выбор при создании нового
			else:
				# Завершаем рисование текущего прямоугольника
				pos = self._clamp_position(event.pos())
				self.current_rect = (self.current_rect[0], pos, self.current_rect[2])
				self.rectangles.append(self.current_rect)
				self.current_rect = None
			self.update()
		elif event.button() == Qt.RightButton:
			print("Right mouse button clicked")
			# Проверяем, попал ли клик на существующий прямоугольник
			pos = event.pos()
			for i, rect in enumerate(self.rectangles):
				if self._is_point_in_rect(pos, rect):
					if i == self.selected_rect_index:
						# Если прямоугольник уже выбран, удаляем его
						self.rectangles.pop(i)
						self.selected_rect_index = -1
					else:
						# Выбираем прямоугольник
						self.selected_rect_index = i
					self.update()
					break
		self.parent._update_ui_state()

	def mouseMoveEvent(self, event):
		if self.current_rect is not None:
			# Обновляем конечную точку текущего прямоугольника
			pos = self._clamp_position(event.pos())
			self.current_rect = (self.current_rect[0], pos, self.current_rect[2])
			self.update()

	def _clamp_position(self, pos):
		"""Ограничивает позицию в пределах виджета"""
		x = max(0, min(pos.x(), self.width()))
		y = max(0, min(pos.y(), self.height()))
		return QPoint(x, y)

	def _is_point_in_rect(self, point, rect):
		"""Проверяет, находится ли точка внутри прямоугольника"""
		start, end, _ = rect
		x1, y1 = min(start.x(), end.x()), min(start.y(), end.y())
		x2, y2 = max(start.x(), end.x()), max(start.y(), end.y())
		return x1 <= point.x() <= x2 and y1 <= point.y() <= y2

	def resizeEvent(self, event: QResizeEvent):
		super().resizeEvent(event)
		self.processor.buffer_dirty = True  # Buffer size changed, needs redraw

	def render_to_buffer(self):
		if self.width() <= 0 or self.height() <= 0:
			self._buffer = None
			return
		self._buffer = QPixmap(self.size())
		# Fill with a transparent background, actual frame background is handled by super().paintEvent and stylesheet
		self._buffer.fill(Qt.transparent)

		painter = QPainter(self._buffer)
		painter.setRenderHint(QPainter.Antialiasing)

		# --- Start of drawing logic, now on the buffer's painter ---
		if not self.processor.texture_data:
			painter.end()  # Crucial to end painting on buffer
			self.processor.buffer_dirty = False
			return

		font = painter.font()  # Get default font
		font.setPointSize(10)
		painter.setFont(font)

		widget_w = self.width()
		widget_h = self.height()

		# print(">>>RELATIVE SIZE:", self.processor.relative_size)
		if self.processor.half_square:
			scale = min(widget_w * self.processor.relative_size, widget_h * self.processor.relative_size * 2)
		else:
			scale = min(widget_w * self.processor.relative_size, widget_h * self.processor.relative_size)

		for i, tex_info in enumerate(self.processor.texture_data):
			# u, v are normalized (0-1) coordinates *within the current_atlas_draw_size*
			uv_pos = tex_info.get('position', ((0.0, 0.0), (0.0, 0.0)))  # Default to avoid error
			u_coords, v_coords = uv_pos[0], uv_pos[1]

			# Ensure coordinates are valid tuples/lists of two numbers
			if not (isinstance(u_coords, (list, tuple)) and len(u_coords) == 2 and
			        isinstance(v_coords, (list, tuple)) and len(v_coords) == 2):
				# print(f"Warning: Invalid position format for texture: {tex_info.get('texture_name', 'N/A')}")
				continue

			# w_norm, h_norm are normalized width/height (0-1) of the texture within the atlas
			w_norm = u_coords[1] - u_coords[0]
			h_norm = v_coords[1] - v_coords[0]

			if w_norm <= 0 or h_norm <= 0:  # Skip zero-size textures
				continue

			rect_w_on_widget = w_norm * scale
			rect_h_on_widget = h_norm * scale

			final_rect_x = u_coords[0] * scale
			final_rect_y = widget_h - (v_coords[1] * scale)  # y-flip

			qt_rect = QRectF(
				final_rect_x,
				final_rect_y,
				rect_w_on_widget,
				rect_h_on_widget
			)

			pixmap = self.processor.loaded_pixmaps.get(i)
			if pixmap:
				# print("PIXMAP:", pixmap)
				source_rect = QRectF(0, 0, pixmap.width(), pixmap.height())
				painter.drawPixmap(qt_rect, pixmap, source_rect)
				painter.setPen(QPen(QColor(255, 255, 255), 1))  #
				painter.drawRect(qt_rect)
			else:
				painter.setPen(QPen(QColor(255, 255, 255), 1))
				painter.setBrush(QBrush(QColor(100, 100, 100, 180)))  # Semi-transparent fill
				painter.drawRect(qt_rect)

			if 'texture_name' in tex_info:
				painter.setPen(QColor(230, 230, 230))
				# Ensure text fits, simple check
				if qt_rect.width() > 5 and qt_rect.height() > 5:
					painter.drawText(qt_rect, Qt.AlignCenter | Qt.TextWordWrap, tex_info['texture_name'])

			# Texture Bake values
			self.texture_bake_position.append((u_coords[0], v_coords[1]))

		# Draw a border around the entire canvas area
		# Draw display_canvas_dimension text at bottom center of the drawn canvas area
		painter.setPen(QPen(QColor(255, 255, 0)))
		text_rect = QRectF(0, self.height() - 17, self.width(), 20)
		painter.drawText(text_rect, Qt.AlignHCenter, str(self.processor.canvas_dimension))

		# Draw grid lines
		self._draw_grid(painter)

		# --- End of drawing logic ---

		painter.end()  # IMPORTANT: Release the painter for the buffer
		self.processor.buffer_dirty = False

	def render_to_buffer_atlas(self, image):
		print("render_to_buffer_atlas")
		if self.width() <= 0 or self.height() <= 0:
			self._buffer = None
			return
		self._buffer = QPixmap(self.size())
		# Fill with a transparent background, actual frame background is handled by super().paintEvent and stylesheet
		self._buffer.fill(Qt.transparent)

		painter = QPainter(self._buffer)
		painter.setRenderHint(QPainter.Antialiasing)
		widget_w = self.width()
		widget_h = self.height()
		uv_pos = ((0.0, 0.0), (0.0, 0.0))  # Default to avoid error
		u_coords, v_coords = uv_pos[0], uv_pos[1]

		scale = min(widget_w * self.processor.relative_size, widget_h * self.processor.relative_size)
		rect_w_on_widget = 1.0 * scale
		rect_h_on_widget = 1.0 * scale

		qt_rect = QRectF(
			0,
			0,
			rect_w_on_widget,
			rect_h_on_widget
		)

		source_rect = QRectF(0, 0, image.width(), image.height())

		painter.drawPixmap(qt_rect, image, source_rect)
		painter.setPen(QPen(QColor(255, 255, 255), 1))  #
		painter.drawRect(source_rect)

		''' '''

		painter.end()  # IMPORTANT: Release the painter for the buffer
		self.processor.buffer_dirty = False

	def _draw_grid(self, painter):
		"""
		Draws horizontal and vertical grid lines dividing the preview area into 4 equal parts.
		"""
		widget_w = self.width()
		widget_h = self.height()

		grid_width_count = grid_height_count = self.processor.canvas_dimension // 512
		if (self.processor.half_square):
			grid_height_count = grid_width_count // 2

		painter.setPen(QPen(QColor(0, 0, 255, 100), 1, Qt.DashLine))  # Semi-transparent blue dashed lines

		# Draw vertical lines
		for i in range(1, grid_width_count):
			x = (widget_w / grid_width_count) * i
			painter.drawLine(x, 0, x, widget_h)

		# Draw horizontal lines
		for i in range(1, grid_height_count):
			y = (widget_h / grid_height_count) * i
			painter.drawLine(0, y, widget_w, y)

	def paintEvent(self, event: QPaintEvent):
		# This first call to super().paintEvent will draw the QFrame's background
		# as per stylesheet (e.g., "background-color: gray;") and its border.
		super(PreviewFrame, self).paintEvent(event)

		if self.processor.buffer_dirty:
			self.render_to_buffer()

		painter = QPainter(self)

		if self._buffer and not self._buffer.isNull():
			painter.drawPixmap(0, 0, self._buffer)
		else:
			print("Buffer is None or Null, or not dirty. Nothing to draw from buffer.")

		''' Draw rectangles if in draw mode '''
		for i, rect in enumerate(self.rectangles):
			start, end, color = rect
			x = min(start.x(), end.x())
			y = min(start.y(), end.y())
			width = abs(end.x() - start.x())
			height = abs(end.y() - start.y())

			# Если прямоугольник выбран, рисуем его с другим цветом или обводкой
			if i == self.selected_rect_index:
				painter.setPen(QPen(QColor(255, 255, 0), 2))  # Желтая обводка для выбранного
				painter.setBrush(QBrush(color))
			else:
				painter.setPen(QPen(QColor(255, 255, 255), 1))
				painter.setBrush(QBrush(color))

			painter.drawRect(x, y, width, height)

		# Рисуем текущий прямоугольник, если он есть
		if self.current_rect:
			start, end, color = self.current_rect
			x = min(start.x(), end.x())
			y = min(start.y(), end.y())
			width = abs(end.x() - start.x())
			height = abs(end.y() - start.y())

			painter.setPen(QPen(QColor(255, 255, 255), 1, Qt.DashLine))
			painter.setBrush(QBrush(color))
			painter.drawRect(x, y, width, height)
		''' Draw rectangles if in draw mode '''

		if self.processor.success:
			self.paint_info(painter, "Atlas Pack Successful!")
		else:
			# Determine the message to display
			message = self._get_status_message()
			if message:
				self.paint_info(painter, message)

	def _get_status_message(self):
		"""
		Determines the message to display depending on the processor's state.
		"""
		if not self.processor:
			return "Save the file \nbefore using the tool\n\nSelect mesh(s) or material(s)\nor combination of both"

		# Check the loading state
		if hasattr(self.processor, 'loading_state'):
			if self.processor.loading_state == 'initial':
				return "Save the file \nbefore using the tool\n\nSelect mesh(s) or material(s)\nor combination of both"
			elif self.processor.loading_state == 'loading_textures':
				if self.processor.current_loading_texture:
					return f"Loading texture {self.processor.current_loading_texture}"
				else:
					return "Loading textures..."
			elif self.processor.loading_state == 'processing_atlas':
				return "Creating atlas..."
			elif self.processor.loading_state == 'completed':
				return None  # Don't show message upon completion

		# If there is no texture data and the file is not saved
		if not self.processor.texture_data:
			if hasattr(self.processor, 'is_file_saved') and not self.processor.is_file_saved:
				return "Save the file \nbefore using the tool\n\nSelect mesh(s) or material(s)\nor combination of both"

		return None

	def paint_info(self, painter, message='Test'):
		"""
		Custom method to paint additional information on the preview.
		Currently not used, but can be extended for more complex UI elements.
		"""
		font = painter.font()  # Get default font
		font.setPointSize(16)
		font.setBold(True)
		painter.setFont(font)

		painter.setPen(QColor(255, 255, 0))  # Yellow color
		text_rect = QRectF(0, 0, self.width(), self.height())
		painter.drawText(text_rect, Qt.AlignCenter, message)
		painter.end()
