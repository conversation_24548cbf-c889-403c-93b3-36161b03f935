from pyside_compat import *
import traceback
import os


class PreviewFrame(QFrame):
	def __init__(self, parent=None, processor=None):
		super(PreviewFrame, self).__init__(parent)
		self.processor = processor

		self.texture_bake_position = []
		self.setStyleSheet("background-color: gray;")
		self.setMinimumSize(300, 300)  # Set minimum size for the preview frame

		self._buffer = None  # Off-screen buffer for drawing

	def resizeEvent(self, event: QResizeEvent):
		super().resizeEvent(event)
		self.processor.buffer_dirty = True  # Buffer size changed, needs redraw

	def render_to_buffer(self):
		if self.width() <= 0 or self.height() <= 0:
			self._buffer = None
			return
		self._buffer = QPixmap(self.size())
		# Fill with a transparent background, actual frame background is handled by super().paintEvent and stylesheet
		self._buffer.fill(Qt.transparent)

		painter = QPainter(self._buffer)
		painter.setRenderHint(QPainter.Antialiasing)

		# --- Start of drawing logic, now on the buffer's painter ---
		if not self.processor.texture_data:
			painter.end()  # Crucial to end painting on buffer
			self.processor.buffer_dirty = False
			return

		font = painter.font()  # Get default font
		font.setPointSize(10)
		painter.setFont(font)

		widget_w = self.width()
		widget_h = self.height()

		# print(">>>RELATIVE SIZE:", self.processor.relative_size)
		if self.processor.half_square:
			scale = min(widget_w * self.processor.relative_size, widget_h * self.processor.relative_size * 2)
		else:
			scale = min(widget_w * self.processor.relative_size, widget_h * self.processor.relative_size)

		for i, tex_info in enumerate(self.processor.texture_data):
			# u, v are normalized (0-1) coordinates *within the current_atlas_draw_size*
			uv_pos = tex_info.get('position', ((0.0, 0.0), (0.0, 0.0)))  # Default to avoid error
			u_coords, v_coords = uv_pos[0], uv_pos[1]

			# Ensure coordinates are valid tuples/lists of two numbers
			if not (isinstance(u_coords, (list, tuple)) and len(u_coords) == 2 and
			        isinstance(v_coords, (list, tuple)) and len(v_coords) == 2):
				# print(f"Warning: Invalid position format for texture: {tex_info.get('texture_name', 'N/A')}")
				continue

			# w_norm, h_norm are normalized width/height (0-1) of the texture within the atlas
			w_norm = u_coords[1] - u_coords[0]
			h_norm = v_coords[1] - v_coords[0]

			if w_norm <= 0 or h_norm <= 0:  # Skip zero-size textures
				continue

			rect_w_on_widget = w_norm * scale
			rect_h_on_widget = h_norm * scale

			final_rect_x = u_coords[0] * scale
			final_rect_y = widget_h - (v_coords[1] * scale)  # y-flip

			qt_rect = QRectF(
				final_rect_x,
				final_rect_y,
				rect_w_on_widget,
				rect_h_on_widget
			)

			pixmap = self.processor.loaded_pixmaps.get(i)
			if pixmap:
				# print("PIXMAP:", pixmap)
				source_rect = QRectF(0, 0, pixmap.width(), pixmap.height())
				painter.drawPixmap(qt_rect, pixmap, source_rect)
				painter.setPen(QPen(QColor(255, 255, 255), 1))  #
				painter.drawRect(qt_rect)
			else:
				painter.setPen(QPen(QColor(255, 255, 255), 1))
				painter.setBrush(QBrush(QColor(100, 100, 100, 180)))  # Semi-transparent fill
				painter.drawRect(qt_rect)

			if 'texture_name' in tex_info:
				painter.setPen(QColor(230, 230, 230))
				# Ensure text fits, simple check
				if qt_rect.width() > 5 and qt_rect.height() > 5:
					painter.drawText(qt_rect, Qt.AlignCenter | Qt.TextWordWrap, tex_info['texture_name'])

			# Texture Bake values
			self.texture_bake_position.append((u_coords[0], v_coords[1]))

		# Draw a border around the entire canvas area
		# Draw display_canvas_dimension text at bottom center of the drawn canvas area
		painter.setPen(QPen(QColor(255, 255, 0)))
		text_rect = QRectF(0, self.height() - 17, self.width(), 20)
		painter.drawText(text_rect, Qt.AlignHCenter, str(self.processor.canvas_dimension))

		# Draw grid lines
		self._draw_grid(painter)

		# --- End of drawing logic ---

		painter.end()  # IMPORTANT: Release the painter for the buffer
		self.processor.buffer_dirty = False

	def _draw_grid(self, painter):
		"""
		Draws horizontal and vertical grid lines dividing the preview area into 4 equal parts.
		"""
		widget_w = self.width()
		widget_h = self.height()

		painter.setPen(QPen(QColor(0, 0, 255, 100), 1, Qt.DashLine))  # Semi-transparent blue dashed lines

		# Draw vertical lines
		for i in range(1, 4):
			x = (widget_w / 4) * i
			painter.drawLine(x, 0, x, widget_h)

		# Draw horizontal lines
		for i in range(1, 4):
			y = (widget_h / 4) * i
			painter.drawLine(0, y, widget_w, y)

	def paintEvent(self, event: QPaintEvent):
		# This first call to super().paintEvent will draw the QFrame's background
		# as per stylesheet (e.g., "background-color: gray;") and its border.
		super(PreviewFrame, self).paintEvent(event)

		if self.processor.buffer_dirty:
			self.render_to_buffer()

		painter = QPainter(self)

		if self._buffer and not self._buffer.isNull():
			painter.drawPixmap(0, 0, self._buffer)
		else:
			print("Buffer is None or Null, or not dirty. Nothing to draw from buffer.")

		if self.processor.success:
			self.paint_info(painter, "Atlas Pack Successful!")
		else:
			# Determine the message to display
			message = self._get_status_message()
			if message:
				self.paint_info(painter, message)

	def _get_status_message(self):
		"""
		Determines the message to display depending on the processor's state.
		"""
		if not self.processor:
			return "Save the file \nbefore using the tool\n\nSelect mesh(s) or material(s)\nor combination of both"

		# Check the loading state
		if hasattr(self.processor, 'loading_state'):
			if self.processor.loading_state == 'initial':
				return "Save the file \nbefore using the tool\n\nSelect mesh(s) or material(s)\nor combination of both"
			elif self.processor.loading_state == 'loading_textures':
				if self.processor.current_loading_texture:
					return f"Loading texture {self.processor.current_loading_texture}"
				else:
					return "Loading textures..."
			elif self.processor.loading_state == 'processing_atlas':
				return "Creating atlas..."
			elif self.processor.loading_state == 'completed':
				return None  # Don't show message upon completion

		# If there is no texture data and the file is not saved
		if not self.processor.texture_data:
			if hasattr(self.processor, 'is_file_saved') and not self.processor.is_file_saved:
				return "Save the file \nbefore using the tool\n\nSelect mesh(s) or material(s)\nor combination of both"

		return None

	def paint_info(self, painter, message='Test'):
		"""
		Custom method to paint additional information on the preview.
		Currently not used, but can be extended for more complex UI elements.
		"""
		font = painter.font()  # Get default font
		font.setPointSize(16)
		font.setBold(True)
		painter.setFont(font)

		painter.setPen(QColor(255, 255, 0))  # Yellow color
		text_rect = QRectF(0, 0, self.width(), self.height())
		painter.drawText(text_rect, Qt.AlignCenter, message)
		painter.end()
