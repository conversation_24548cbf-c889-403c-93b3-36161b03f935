from pyside_compat import *
import maya.OpenMayaUI as omui
import functools
import maya.cmds as cmds
from ..core.processor import AtlasProcessor
from .widgets.preview_widget import PreviewFrame
from .widgets.decomposition_groupbox import DecompositionGroupBox
from ..utils.atlas_generator import TextureAtlasCreator


def undoable(func):
	"""Декоратор, который делает функцию отменяемой в Maya"""

	@functools.wraps(func)
	def wrapper(*args, **kwargs):
		# Название операции в истории undo будет соответствовать имени функции
		cmds.undoInfo(openChunk=True, chunkName=func.__name__)
		try:
			return func(*args, **kwargs)
		finally:
			cmds.undoInfo(closeChunk=True)

	return wrapper


class MainWindow(QMainWindow):
	_instance = None

	@staticmethod
	def get_instance():
		if MainWindow._instance is None:
			MainWindow._instance = MainWindow(parent=get_maya_main_window())
		else:
			MainWindow._instance.raise_()
			MainWindow._instance.activateWindow()
		return MainWindow._instance

	def __init__(self, parent=None):
		if MainWindow._instance is not None:
			raise RuntimeError("MainWindow instance already exists. Use get_instance().")

		super(MainWindow, self).__init__(parent)
		MainWindow._instance = self

		# self.atlas_dimension = 1024  # Default dimension for the atlas
		self.output_directory = None
		self.filename = "texture_atlas"

		# State tracking for button logic
		self.has_generated_atlas = False  # Track if Generate button has been clicked at least once

		self.processor = AtlasProcessor()
		self.atlas_generator = TextureAtlasCreator(processor=self.processor)

		self.setWindowTitle("Atlas Pack Tool")
		self.setGeometry(100, 100, 400, 300)
		self.setObjectName("AtlasPackMainWindow")

		# Setup UI components
		self._setup_ui()
		self._connect_signals()

		# Restore window position
		self.settings = QSettings("AtlasPackTool", "MainWindow")
		self.restore_geometry()

		self.set_size_preview()
		self._update_ui_state()

	def _setup_ui(self):
		self.central_widget = QWidget()
		self.setCentralWidget(self.central_widget)
		self.main_layout = QVBoxLayout(self.central_widget)

		self._setup_preview_section()
		self._setup_packing_section()
		self._setup_atlas_section()
		self._setup_atlas_decomposition()
		self.main_layout.addStretch(1)

	def _setup_preview_section(self):
		self.image_widget = QWidget()
		self.image_widget.setMinimumSize(350, 350)
		self.image_layout = QVBoxLayout(self.image_widget)
		self.image_layout.setContentsMargins(0, 0, 0, 0)  # Remove margins
		self.image_layout.setAlignment(Qt.AlignCenter)  # Center the content

		self.image_frame = PreviewFrame(parent=self, processor=self.processor)

		# Set the widget reference in the processor for updates
		self.processor.preview_widget = self.image_frame  # Set the widget reference in the processor for updates
		self.image_layout.addWidget(self.image_frame)
		self.main_layout.addWidget(self.image_widget, 1)

	def _setup_packing_section(self):
		self.packing_group_box = QGroupBox("Atlas Packing Preview")
		self.packing_layout = QGridLayout(self.packing_group_box)

		self.pack_square_button = QPushButton("Pack Square")
		self.pack_square_button.setToolTip("Generates a preview by packing textures as squares.")
		self.pack_half_button = QPushButton("Pack Half")
		self.pack_half_button.setToolTip("Generates a preview by packing textures as horizontal half square.")

		self.packing_layout.addWidget(self.pack_square_button, 0, 0)
		self.packing_layout.addWidget(self.pack_half_button, 0, 1)

		self.main_layout.addWidget(self.packing_group_box)

	def _setup_atlas_section(self):
		self.output_group_box = QGroupBox("Atlas Generation")
		self.output_layout = QFormLayout(self.output_group_box)
		self.output_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)  # Fields will expand

		# Directory Path
		self.path_layout = QHBoxLayout()
		self.output_dir_line_edit = QLineEdit()
		self.output_dir_line_edit.setPlaceholderText("e.g., D:/Projects/MyGame/Textures")
		self.output_dir_line_edit.setToolTip("Directory where the final texture atlas will be saved.")
		self.browse_button = QPushButton("Browse...")
		self.browse_button.setToolTip("Open a dialog to select the output directory.")
		self.path_layout.addWidget(self.output_dir_line_edit)
		self.path_layout.addWidget(self.browse_button)
		self.output_layout.addRow("Output Directory:", self.path_layout)

		# Filename - создаем такой же layout как для директории
		self.filename_layout = QHBoxLayout()
		self.output_filename_line_edit = QComboBox()
		self.output_filename_line_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
		self.output_filename_line_edit.setMinimumHeight(22)
		self.output_filename_line_edit.setEditable(True)  # Позволяет вводить текст
		self.output_filename_line_edit.setToolTip(
			"Name of the final material and texture atlas file (e.g., atlas_turret, atlas_hull).")

		# Создаем невидимую кнопку той же ширины что и Browse для выравнивания #WTF???
		self.filename_spacer = QWidget(self)
		self.filename_spacer.setFixedWidth(60)
		self.filename_spacer.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)

		# Добавляем комбобокс в layout, чтобы он занимал то же пространство что и поле директории
		self.filename_layout.addWidget(self.output_filename_line_edit)
		self.filename_layout.addWidget(self.filename_spacer)
		self.output_layout.addRow("Filename:", self.filename_layout)

		self._populate_atlas_materials()

		self.generate_atlas_button = QPushButton("Generate Atlas")
		self.generate_atlas_button.setToolTip("Creates the final texture atlas image file using the settings above.")
		self.generate_atlas_button.setFixedHeight(30)  # Make the button more noticeable
		self.generate_atlas_button.setEnabled(False)  # Initially inactive

		self.restore_button = QPushButton("Restore")
		self.restore_button.setToolTip("Restore the previous state before atlas packing.")
		self.restore_button.setFixedHeight(30)
		self.restore_button.setEnabled(False)  # Initially disabled until Generate is clicked

		# Add the atlas generation and restore buttons together
		self.output_action_layout = QHBoxLayout()
		self.output_action_layout.addWidget(self.generate_atlas_button)
		self.output_action_layout.addWidget(self.restore_button)
		self.output_layout.addRow(self.output_action_layout)  # Add as a row in QFormLayout

		self.main_layout.addWidget(self.output_group_box)

	def _setup_atlas_decomposition(self):
		"""Setup the atlas decomposition section using the DecompositionGroupBox widget"""
		self.decomposition_groupbox = DecompositionGroupBox(processor=self.processor, parent=self)

		# Store references to the widgets for backward compatibility
		self.edit_group_box = self.decomposition_groupbox
		self.draw_rect_button = self.decomposition_groupbox.draw_rect_button
		self.output_atlasname_line_edit = self.decomposition_groupbox.output_atlasname_line_edit
		self.decompose_button = self.decomposition_groupbox.decompose_button

		self.main_layout.addWidget(self.decomposition_groupbox)

	def _connect_signals(self):
		"""Connect UI signals to their respective slots"""
		self.pack_square_button.clicked.connect(self._on_pack_clicked)
		self.pack_half_button.clicked.connect(lambda: self._on_pack_clicked(half=True))
		self.browse_button.clicked.connect(self._on_browse_clicked)
		self.generate_atlas_button.clicked.connect(self._on_atlas_clicked)
		self.restore_button.clicked.connect(self._on_restore_clicked)

		self.draw_rect_button.clicked.connect(self._on_draw_rect_clicked)
		self.output_atlasname_line_edit.currentIndexChanged.connect(self._update_ui_state)

		self.output_dir_line_edit.textChanged.connect(self._on_directory_changed)
		self.output_filename_line_edit.editTextChanged.connect(self._on_filename_changed)
		self.decompose_button.clicked.connect(self._on_decompose_clicked)

	def _on_browse_clicked(self):
		"""Handle browse button click"""
		directory = QFileDialog.getExistingDirectory(
			self,
			"Select Output Directory",
			self.output_directory,
			QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
		)
		if directory:
			self.output_directory = directory
			self.output_dir_line_edit.setText(directory)

	def _on_directory_changed(self, text):
		"""Handle directory input change"""
		self.output_directory = text

	def _set_placeholder_style(self):
		"""Устанавливает стиль placeholder для комбобокса"""
		# Применяем стиль для placeholder текста
		self.output_filename_line_edit.lineEdit().setStyleSheet(
			"QLineEdit { color: gray; font-style: italic; }"
		)

	def _clear_placeholder_style(self):
		"""Убирает стиль placeholder для комбобокса"""
		self.output_filename_line_edit.lineEdit().setStyleSheet("")

	def _populate_atlas_materials(self, file_name=None):
		"""Находит в сцене все материалы с именем atlas* и добавляет их в выпадающий список"""
		try:
			all_materials = cmds.ls(materials=True)
			# Фильтруем материалы, имена которых начинаются с "atlas"
			atlas_materials = [mat for mat in all_materials if mat.lower().startswith('atlas')]
			self.output_filename_line_edit.clear()

			# Добавляем найденные atlas материалы
			if not atlas_materials:
				self.output_filename_line_edit.lineEdit().setPlaceholderText("Type name of material/atlas")
				self._set_placeholder_style()
			else:
				# Добавляем найденные atlas материалы
				for material in atlas_materials:
					self.output_filename_line_edit.addItem(material)
				self._clear_placeholder_style()

			if file_name:
				self.output_filename_line_edit.setCurrentText(file_name)

		except Exception as e:
			print(f"Error populating atlas materials: {e}")
			# В случае ошибки устанавливаем placeholder
			self.output_filename_line_edit.clear()
			self.output_filename_line_edit.lineEdit().setPlaceholderText("Type name of material/atlas")
			self._set_placeholder_style()

	def _on_filename_changed(self, text):
		"""Handle filename input change"""
		self.filename = text

	def _on_draw_rect_clicked(self):
		"""Обработчик нажатия на кнопку рисования прямоугольников"""
		self.image_frame.toggle_draw_mode()
		# Обновляем состояние кнопки в соответствии с режимом рисования
		self.draw_rect_button.setChecked(self.image_frame.draw_mode)

		if self.image_frame.draw_mode:
			self.setFixedSize(self.size())  # Фиксируем текущий размер окна
			pixmap = self.processor.load_pixmap_atlas(self.output_atlasname_line_edit.currentText())
			self.set_size_preview()
			self.image_frame.render_to_buffer_atlas(pixmap)
			self.image_frame.update()

		# self._update_ui_state()

		else:
			self.setMinimumSize(0, 0)
			self.setMaximumSize(16777215, 16777215)  # Максимально возможные значения

		self._update_ui_state()

	def _on_decompose_clicked(self):
		"""Handle Decompose button click"""
		print("Decompose button clicked - functionality not implemented yet")
		# TODO: Implement decomposition functionality
		pass

	def _update_ui_state(self):
		print(f"Updating UI state. Draw mode: , ")
		"""Updates the enabled/disabled state of buttons based on application state."""
		has_packed_data = bool(self.processor.texture_data)
		self.generate_atlas_button.setEnabled(has_packed_data)

		# Restore button should only be enabled after Generate has been clicked at least once
		self.restore_button.setEnabled(self.has_generated_atlas)

		can_decompose = True

	# Condition 1: self.draw_rect_button is checked
	# if not self.draw_rect_button.isChecked():
	# 	can_decompose = False
	#
	# # Condition 2: self.output_atlasname_line_edit.currentIndex() is not empty (i.e., an item is selected)
	# # A currentIndex of -1 means no item is selected.
	# if self.output_atlasname_line_edit.currentIndex() == -1:
	# 	print("No item selected")
	# 	can_decompose = False
	#
	# # Condition 3: self.image_frame.rectangles is not empty
	# # Assuming self.image_frame has an attribute 'rectangles' which is a list
	# if not hasattr(self.image_frame, 'rectangles') or not self.image_frame.rectangles:
	# 	print("No rectangles found")
	# 	can_decompose = False

	# self.decompose_button.setEnabled(can_decompose)

	def closeEvent(self, event):
		self.save_geometry()
		MainWindow._instance = None
		event.accept()

	def save_geometry(self):
		self.settings.setValue("geometry", self.saveGeometry())
		self.settings.setValue("output_directory", self.output_directory)
		self.settings.setValue("filename", self.filename)

	def restore_geometry(self):
		geometry = self.settings.value("geometry")
		if geometry:
			self.restoreGeometry(geometry)

		saved_directory = self.settings.value("output_directory")
		if saved_directory:
			self.output_directory = saved_directory
			self.output_dir_line_edit.setText(saved_directory)

		saved_filename = self.settings.value("filename")
		if saved_filename and saved_filename.strip():
			self.filename = saved_filename
			self.output_filename_line_edit.setCurrentText(saved_filename)
			self._clear_placeholder_style()
		else:
			# Если нет сохраненного имени файла, проверяем материалы
			self._populate_atlas_materials()

	def set_size_preview(self):
		available_width = self.image_widget.width()
		available_height = self.image_widget.height()

		if self.processor.half_square:
			width = max(available_width, 350)
			height = width // 2
			self.image_frame.setFixedSize(width, height)
			self.image_widget.setMinimumSize(350, 175)  # 350 / 2 = 175
		else:
			size = min(available_width, available_height)
			size = max(size, 350)  # Не меньше 350px
			self.image_frame.setFixedSize(size, size)
			self.image_widget.setMinimumSize(350, 350)

	def _on_pack_clicked(self, half=False):
		"""Handle Pack/Re-pack button click"""
		self.processor.success = False  # Reset the success flag before new processing
		self.processor.half_square = half  # Set the flag for half square
		self.processor.texture_proc(half)  # Use the method from AtlasProcessor
		self.set_size_preview()
		self.image_frame.render_to_buffer()
		self.image_frame.update()

		self._update_ui_state()

	@undoable
	def mapping_assign(self):
		atlas_faces = self.processor.mapping_proc()
		self.processor.assign_material_to_faces(self.filename, atlas_faces)

	def _on_atlas_clicked(self):
		"""Handle Atlas button click"""
		self.processor.success = False  # Reset the success flag before new processing
		self.image_frame.update()
		self.atlas_generator.set_output_path(f"{self.output_directory}/{self.filename}")
		self.processor.success = self.atlas_generator.write_atlas_to_file()  # Create the atlas and save it to a file
		if self.processor.success:
			self.mapping_assign()
			self.image_frame.update()
			self.processor.loading_state = 'completed'

		# Enable the Restore button after Generate has been clicked (regardless of success)
		# This ensures users can restore even if generation partially failed
		self.has_generated_atlas = True
		self._update_ui_state()
		self._populate_atlas_materials(self.filename)

	def _on_restore_clicked(self):
		"""Restore the previous state before atlas packing"""
		self.processor.restore_initial_state(self.filename)
		self.processor.texture_data.clear()
		self.processor.success = False
		self.image_frame.texture_bake_position = []
		self.image_frame._buffer = None  # Clear the buffer
		self.image_frame.render_to_buffer()  # Render empty buffer
		self.image_frame.update()  # Force update of the widget
		self.processor.half_square = False
		self.set_size_preview()
		self._update_ui_state()
		pass

	def resizeEvent(self, event):
		"""Support square form of image_widget when the window size changes"""
		super().resizeEvent(event)
		self.set_size_preview()

	def update_filename_dropdown(self):
		"""Обновляет выпадающий список с именами материалов"""
		current_text = self.output_filename_line_edit.currentText()

		self._populate_atlas_materials()

		# Добавляем имена материалов из processor (без расширений)
		for data in self.processor.texture_data:
			if "material_name" in data:
				material_name = data["material_name"]

				# Проверяем, что такого элемента еще нет в списке
				items = [self.output_filename_line_edit.itemText(i)
				         for i in range(self.output_filename_line_edit.count())]
				if material_name not in items:
					self.output_filename_line_edit.addItem(material_name)

		# Восстанавливаем текущий текст если он был
		if current_text and current_text.strip():
			self.output_filename_line_edit.setCurrentText(current_text)
			self._clear_placeholder_style()


# Maya parent window
def get_maya_main_window():
	main_window_ptr = omui.MQtUtil.mainWindow()
	return wrapInstance(int(main_window_ptr), QWidget)
