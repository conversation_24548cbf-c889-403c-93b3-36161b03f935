import importlib
import sceneTools.atlasPack.ui.main_window as AtlasPackWnd

# importlib.reload(AtlasPackWnd)
import maya.cmds as cmds
import sys


def show_main_window():
	if cmds.window("AtlasPackMainWindow", exists=True):
		cmds.deleteUI("AtlasPackMainWindow")
	try:
		main_window = AtlasPackWnd.MainWindow.get_instance()
		main_window.setParent(AtlasPackWnd.get_maya_main_window())
		main_window.show()
		main_window.set_size_preview()
	except RuntimeError as e:
		print(e)


def reload_all_modules(module_name):
	print('loaded {}'.format(module_name))
	for m in list(sys.modules):
		if module_name in m:
			print('deleted {}'.format(m))
			del (sys.modules[m])


def main():
	"""Main function to run the Atlas Pack tool."""
	reload_all_modules('atlasPack')
	show_main_window()


# Example usage
if __name__ == "__main__":
	reload_all_modules('atlasPack')
	show_main_window()
