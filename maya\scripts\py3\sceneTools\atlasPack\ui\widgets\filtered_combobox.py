from pyside_compat import *
import maya.cmds as cmds


class FilteredComboBox(QComboBox):
	def __init__(self, parent=None):
		super(FilteredComboBox, self).__init__(parent)

		self.material_list = []

		self.setEditable(True)
		# Важно: QComboBox.NoInsert, чтобы пользователь не мог добавлять новые элементы,
		# которых нет в исходном списке, просто напечатав их.
		self.setInsertPolicy(QComboBox.NoInsert)

		# Модель для хранения всех элементов (всех материалов)
		self._source_model = QStandardItemModel(self)

		# Прокси-модель для фильтрации
		self._proxy_model = QSortFilterProxyModel(self)
		self._proxy_model.setSourceModel(self._source_model)
		self._proxy_model.setFilterCaseSensitivity(Qt.CaseInsensitive)  # Игнорировать регистр
		self._proxy_model.setFilterKeyColumn(0)  # Фильтровать по первому столбцу (текст элемента)

		self.setModel(self._proxy_model)

		# Подключаем сигнал изменения текста в QLineEdit комбобокса
		self.lineEdit().textChanged.connect(self._on_text_changed)

		# Опционально: плейсхолдер
		self.lineEdit().setPlaceholderText("Введите имя материала...")

	def _on_text_changed(self, text):
		"""
		Вызывается при изменении текста в поле ввода.
		Устанавливает фильтр для прокси-модели.
		"""
		# self.material_list = cmds.ls(materials=True)
		# self.populate_items(self.material_list)
		# Мы хотим, чтобы фильтр работал по принципу "содержит"
		# Для этого можно использовать QRegExp (или QRegularExpression в Qt5.12+)
		# или установить filterSyntax в Wildcard и использовать "*"

		# Вариант с QRegularExpression (предпочтительнее для Qt 5.12+)
		# Экранируем текст, чтобы специальные символы regex не ломали поиск
		escaped_text = QRegularExpression.escape(text)
		regex_pattern = f".*{escaped_text}.*"  # ".*" означает "любые символы до и после"

		# Убедимся, что QRegularExpression доступен (для старых версий PySide2/Qt)
		try:
			regex = QRegularExpression(regex_pattern,
			                           QRegularExpression.CaseInsensitiveOption if self._proxy_model.filterCaseSensitivity() == Qt.CaseInsensitive else QRegularExpression.NoPatternOption)
			self._proxy_model.setFilterRegularExpression(regex)
		except AttributeError:
			# Фоллбэк для старых версий Qt, использующих QRegExp
			# print("Warning: QRegularExpression not available, using QRegExp (less robust for complex patterns).")
			# QRegExp использует синтаксис wildcard по умолчанию, если не указан другой
			self._proxy_model.setFilterRegExp(QRegExp(text,
			                                          Qt.CaseInsensitive if self._proxy_model.filterCaseSensitivity() == Qt.CaseInsensitive else Qt.CaseSensitive,
			                                          QRegExp.Wildcard))  # Wildcard позволяет использовать *text*

	def populate_items(self, items_list):
		"""
		Заполняет комбобокс элементами из списка.
		:param items_list: list[str] - список строк для отображения.
		"""
		print(f"FilteredComboBox.populate_items: {items_list}")
		if self.lineEdit():  # Убедимся, что lineEdit существует
			self.lineEdit().blockSignals(True)

		self._source_model.clear()  # Очищаем предыдущие элементы из исходной модели
		# items_list = cmds.ls(materials=True)
		for item_text in items_list:
			standard_item = QStandardItem(item_text)
			self._source_model.appendRow(standard_item)

		self.setCurrentIndex(-1)

		if self.lineEdit():
			# Очищаем текст в поле ввода, чтобы отобразился плейсхолдер
			self.lineEdit().clear()
			self.lineEdit().blockSignals(False)  # Восстанавливаем сигналы

		self._on_text_changed("")

	# После заполнения можно сбросить фильтр или текст, если нужно
	# self.lineEdit().clear()
	# self._on_text_changed("") # Чтобы отобразить все элементы

	def current_selected_text(self):
		"""
		Возвращает текст текущего выбранного элемента в выпадающем списке.
		Учитывает, что моделью является прокси-модель.
		"""
		current_index = self.currentIndex()
		if current_index < 0:  # Ничего не выбрано
			return None

		# Получаем индекс в прокси-модели
		proxy_model_index = self._proxy_model.index(current_index, 0)
		# Получаем данные из прокси-модели
		return self._proxy_model.data(proxy_model_index)

	def selected_source_item_text(self):
		"""
		Возвращает текст выбранного элемента, как он есть в исходной модели.
		Полезно, если нужно получить точное значение без учета фильтрации.
		"""
		current_proxy_idx = self.currentIndex()
		if current_proxy_idx < 0:
			return None

		# Преобразуем индекс прокси-модели в индекс исходной модели
		source_idx = self._proxy_model.mapToSource(self._proxy_model.index(current_proxy_idx, 0))
		if source_idx.isValid():
			return self._source_model.data(source_idx)
		return None

	def get_current_text(self):
		return self.currentText()
