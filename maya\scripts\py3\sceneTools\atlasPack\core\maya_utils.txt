import maya.api.OpenMaya as om2
import os
import math
import maya.cmds as cmds
import traceback


def get_selected_meshes(return_names=False):
	"""
	Returns a list of selected polygon meshes.
	If return_names is True, returns the full DAG path names, otherwise returns MObject instances.
	"""
	selectionList = om2.MGlobal.getActiveSelectionList()
	meshes = []
	for i in range(selectionList.length()):
		dagPath = selectionList.getDagPath(i)
		# Directly selected mesh shape.
		if dagPath.node().hasFn(om2.MFn.kMesh):
			if return_names:
				fnDag = om2.MFnDagNode(dagPath)
				meshes.append(fnDag.fullPathName())
			else:
				meshes.append(dagPath.node())
		else:
			# For transform nodes, check child shapes.
			for j in range(dagPath.childCount()):
				child = dagPath.child(j)
				if child.hasFn(om2.MFn.kMesh):
					if return_names:
						fnDag = om2.MFnDagNode(child)
						meshes.append(fnDag.fullPathName())
					else:
						meshes.append(child)
	return meshes


def get_materials_from_mesh(mesh_name, return_names=False):
	"""
	Returns a list of materials (shading engines) connected to the given mesh.
	If return_names is True, returns the material names, otherwise returns MObject instances.
	"""
	# print('Mesh name:', mesh_name)
	selectionList = om2.MSelectionList()
	selectionList.add(mesh_name)
	dagPath = selectionList.getDagPath(0)
	mfnMesh = om2.MFnMesh(dagPath)
	shadingEngines, _ = mfnMesh.getConnectedSetsAndMembers(0, True)  # Added True for instanceNumber parameter
	materials = []
	for se in shadingEngines:
		if return_names:
			fnDep = om2.MFnDependencyNode(se)
			materials.append(fnDep.name())
		else:
			materials.append(se)
	return materials


def _get_material_from_shading_engine(shading_engine_name):
	"""Helper function to get material from shading engine"""
	sel = om2.MSelectionList()
	sel.add(shading_engine_name)
	se_obj = sel.getDependNode(0)
	se_fn = om2.MFnDependencyNode(se_obj)

	# Get surfaceShader connection
	ss_plug = se_fn.findPlug("surfaceShader", False)
	if ss_plug.isConnected:
		connections = ss_plug.connectedTo(True, False)  # source, destination
		if connections:
			return connections[0].node()
	return None


def get_texture_file_from_material_color(material_name):
	"""
	Returns the texture file path connected to the material's color attribute.
	Only returns the path if it ends with '_am.png', otherwise returns None.
	Uses Maya Python API 2.
	"""
	# Get the material dependency node
	selection = om2.MSelectionList()
	try:
		selection.add(material_name)
	except Exception:
		return None

	material_obj = selection.getDependNode(0)
	fnDep = om2.MFnDependencyNode(material_obj)

	# If it's a shading engine, get the material
	if material_obj.hasFn(om2.MFn.kShadingEngine):
		material_obj = _get_material_from_shading_engine(material_name)
		if not material_obj:
			return None
		fnDep = om2.MFnDependencyNode(material_obj)

	# Get the 'color' plug from the material
	colorPlug = fnDep.findPlug("color", False)

	# Traverse downstream to find connected file node
	itDepGraph = om2.MItDependencyGraph(colorPlug,
	                                    om2.MItDependencyGraph.kDownstream,
	                                    om2.MItDependencyGraph.kBreadthFirst,
	                                    om2.MItDependencyGraph.kNodeLevel)
	while not itDepGraph.isDone():
		depNode = itDepGraph.currentNode()
		fnDep = om2.MFnDependencyNode(depNode)
		if fnDep.typeName == "file":
			filePlug = fnDep.findPlug("fileTextureName", False)
			filePath = filePlug.asString()
			if filePath.lower().endswith("_am.png"):  # Changed to case-insensitive check
				return filePath.lower()
		itDepGraph.next()
	return None


def get_image_dimensions(image_path):
	"""
	Returns a tuple (width, height) for the image at image_path, or None if not available.
	"""
	if not os.path.exists(image_path):
		return None
	try:
		image = om2.MImage()
		image.readFromFile(image_path)
		size = image.getSize()
		return size
	except Exception as e:
		# print(f"Error reading image: {e}")
		return (None, None)


def get_materials_from_selection():
	"""
	Returns a list of unique material names from selection.
	Works with:
	- Selected meshes (gets their materials)
	- Selected materials
	- Combination of both
	"""
	materials = set()
	selection = om2.MGlobal.getActiveSelectionList()

	for i in range(selection.length()):
		try:
			# Try to get the dependency node
			dep_node = selection.getDependNode(i)
			fn_dep = om2.MFnDependencyNode(dep_node)

			# If it's a transform or mesh, try to get materials from it
			if (dep_node.hasFn(om2.MFn.kTransform) or
					dep_node.hasFn(om2.MFn.kMesh)):
				dag_path = selection.getDagPath(i)
				mesh_materials = get_materials_from_mesh(dag_path.fullPathName(), return_names=True)
				materials.update(mesh_materials)

			# If it's a shading engine or material, add it directly
			elif dep_node.hasFn(om2.MFn.kShadingEngine):
				materials.add(fn_dep.name())
			# For materials (lambert, phong, blinn)
			elif (fn_dep.typeName in ["lambert", "phong", "blinn", "standardSurface"]):
				it_dep = om2.MItDependencyGraph(
					dep_node,
					om2.MFn.kShadingEngine,
					om2.MItDependencyGraph.kDownstream,
					om2.MItDependencyGraph.kBreadthFirst
				)
				while not it_dep.isDone():
					sg_node = it_dep.currentNode()
					sg_fn = om2.MFnDependencyNode(sg_node)
					materials.add(sg_fn.name())
					it_dep.next()

		except Exception:
			traceback.print_exc()
			continue

	return list(materials)


def create_proxy_mesh(texture_infos_list):
	"""
	Creates a proxy mesh with N square polygons, where N is the number of unique textures.
	The mesh will be named "proxy_atlas_mesh".
	Each polygon is a 1x1 square in the XY plane, laid out along the X-axis.

	Args:
		texture_infos_list (list): A list of texture information. The length of this list
								   determines the number of polygons.

	Returns:
		str: The name of the created proxy mesh (transform node), or None if no polygons were created.
	"""
	num_polygons = len(texture_infos_list)
	if num_polygons == 0:
		return None

	vertices = om2.MPointArray()
	poly_counts = om2.MIntArray()
	poly_connects = om2.MIntArray()

	# Define vertices
	# Bottom row of vertices: (num_polygons + 1) vertices
	for i in range(num_polygons + 1):
		vertices.append(om2.MPoint(float(i), 0.0, 0.0))
	# Top row of vertices: (num_polygons + 1) vertices
	for i in range(num_polygons + 1):
		vertices.append(om2.MPoint(float(i), 1.0, 0.0))

	# Define polygon counts and connections
	for i in range(num_polygons):
		poly_counts.append(4)  # Each polygon is a quad

		v_bottom_left = i
		v_bottom_right = i + 1
		# Offset for top row vertices: (num_polygons + 1) is the count of vertices in the bottom row
		v_top_left = (num_polygons + 1) + i
		v_top_right = (num_polygons + 1) + i + 1

		# Order: bottom-left, bottom-right, top-right, top-left (for Maya's default winding order)
		poly_connects.append(v_bottom_left)
		poly_connects.append(v_bottom_right)
		poly_connects.append(v_top_right)
		poly_connects.append(v_top_left)

	mesh_fn = om2.MFnMesh()

	# Create a new transform node to be the parent of the mesh shape
	# Using MFnTransform.create() will give it a default name like "transform1"
	transform_mobj = om2.MFnTransform().create()

	# Create the mesh shape itself, parented under the new transform
	# No UVs are created at this stage by default with this create method.
	# UVs will be assigned in a subsequent step.
	# This returns the MObject of the created mesh shape.
	mesh_shape_mobj = mesh_fn.create(vertices, poly_counts, poly_connects, parent=transform_mobj)

	# Set the name of the transform node (parent of the mesh shape)
	transform_dag_node_fn = om2.MFnDagNode(transform_mobj)
	desired_name = "proxy_atlas_mesh"

	# Using # ensures Maya will append a number if the name is taken,
	# guaranteeing a unique name and returning the actual name used.
	actual_name = transform_dag_node_fn.setName(desired_name + "#")

	# mesh_fn.createUVSet("map1")

	# Find maximum dimension across all textures
	global_max_dim = 1.0
	for info in texture_infos_list:
		width = info.get('width')
		height = info.get('height')
		if width and height and width > 0 and height > 0:
			global_max_dim = max(global_max_dim, width, height)
	# print("Maximum dimension:", global_max_dim)

	# Create arrays for UV coordinates
	u_array = om2.MFloatArray()
	v_array = om2.MFloatArray()
	uv_counts = om2.MIntArray([4] * num_polygons)
	uv_ids = om2.MIntArray()

	# Calculate and store all UV coordinates
	for poly_idx in range(num_polygons):
		texture_info = texture_infos_list[poly_idx]
		tex_width = texture_info.get('width')
		tex_height = texture_info.get('height')

		if tex_width is None or tex_height is None or tex_width <= 0 or tex_height <= 0:
			# print(f"Warning: Invalid dimensions for texture {poly_idx}. Using 1x1 aspect.")
			uv_w_relative = 1.0
			uv_h_relative = 1.0
		else:
			# Calculate relative to global maximum dimension
			uv_w_relative = tex_width / global_max_dim
			uv_h_relative = tex_height / global_max_dim

		u_offset = float(poly_idx)

		# Add UVs in correct order (bottom-left, bottom-right, top-right, top-left)
		u_array.append(u_offset)
		u_array.append(u_offset + uv_w_relative)
		u_array.append(u_offset + uv_w_relative)
		u_array.append(u_offset)

		v_array.append(0.0)
		v_array.append(0.0)
		v_array.append(uv_h_relative)
		v_array.append(uv_h_relative)

		# Store indices for UV assignments (4 per polygon)
		base_idx = poly_idx * 4
		uv_ids.append(base_idx)
		uv_ids.append(base_idx + 1)
		uv_ids.append(base_idx + 2)
		uv_ids.append(base_idx + 3)

	# Set all UVs at once
	mesh_fn.setUVs(u_array, v_array, "map1")
	mesh_fn.assignUVs(uv_counts, uv_ids, "map1")

	# Update the mesh
	mesh_fn.updateSurface()

	# print(f"Created proxy mesh: {actual_name} with {num_polygons} polygon(s).")
	return actual_name, global_max_dim


def layout_proxy_uvs(mesh_name, half=False, max_dim=2048):
	"""
	Layouts the UVs of the given mesh using Maya's default UV layout algorithm.
	"""

	# Load plugin Unfold3D.mll
	if not cmds.pluginInfo("Unfold3D.mll", query=True, loaded=True):
		cmds.loadPlugin("Unfold3D.mll")

	# очень важно использовать ls=1, что значит не масштабировать UVs, а использовать их как есть.
	if half:
		# If half is True, we assume the UVs are already in the range [0, 0.5] and we want to layout them in that space.
		cmds.u3dLayout(mesh_name, res=6096, box=(0, 1, 0, 0.5), ls=1)  # magic number 6096
	else:
		cmds.u3dLayout(mesh_name, res=6096, box=(0, 1, 0, 1), ls=1)
	uv_list = cmds.ls(f"{mesh_name}.map[*]")
	result = cmds.polyEvaluate(uv_list, bc2=True)
	width = result[0][1] - result[0][0]  # max U - min U
	height = result[1][1] - result[1][0]  # max V - min V
	return (width, height)


def polygon_box(mesh_name):
	faces_list = cmds.ls(mesh_name + ".f[*]", flatten=True)
	position_list = []
	for face in faces_list:
		result = cmds.polyEvaluate(face, bc2=True)
		if result:
			position_list.append(result)
	return position_list


# TODO
'''
Implement the function `get_polygons_from_material` to fulfill the TODO comment requirements. 
The function should retrieve all polygons from all meshes in the scene 
that have the specified material assigned to them. 
Use Maya Python API 2.0 (OpenMaya) for better performance instead of cmds where possible. 
Return a tuple containing the material name and a list of associated faces. 
Optimize the implementation for performance, especially when dealing with large scenes.
'''


def get_polygons_from_material(sg_obj):
	"""
	Returns a tuple containing the material name and a list of polygons (face names)
	associated with the given material from all meshes in the scene.
	Uses Maya API 2.0 for better performance.
	"""
	# Get the shading group object from string name
	sel_list = om2.MSelectionList()
	try:
		sel_list.add(sg_obj)
	except RuntimeError:
		return (sg_obj, [])

	sg_mobject = sel_list.getDependNode(0)

	face_list = []

	# Use MFnSet to get members (faces) of the shading group
	fn_set = om2.MFnSet(sg_mobject)

	# Get all members of the shading group
	members_sel_list = fn_set.getMembers(False)  # False for flatten=False

	# Iterate through the members
	for i in range(members_sel_list.length()):
		try:
			dag_path, component = members_sel_list.getComponent(i)

			if not cmds.objExists(dag_path.fullPathName()):
				continue

			cmds.delete(dag_path.fullPathName(), ch=True)

			# Check if it's a mesh and has face components
			if dag_path.hasFn(om2.MFn.kMesh):
				# If no component is specified, it means the entire mesh
				if component.isNull():
					mesh_fn = om2.MFnMesh(dag_path)
					num_faces = mesh_fn.numPolygons
					for face_idx in range(num_faces):
						face_name = f"{dag_path.fullPathName()}.f[{face_idx}]"
						face_list.append(face_name)

				# If component is specified and it's face component
				elif component.hasFn(om2.MFn.kMeshPolygonComponent):
					face_it = om2.MItMeshPolygon(dag_path, component)
					while not face_it.isDone():
						face_idx = face_it.index()
						face_name = f"{dag_path.fullPathName()}.f[{face_idx}]"
						face_list.append(face_name)
						face_it.next()

		except RuntimeError:
			# Skip items that can't be processed
			continue

	return (sg_obj, face_list)


def normalize_uv_shell_position(face_list, face_position, uv_set=''):
	"""
	Moves UV shell positions to renormalized position based on face_position.
	First normalizes shell pivot, then normalizes face_position,
	then converts normalized pivot back to [0,1] coordinates and moves shell there.

	Args:
		face_list (list): List of face names (e.g., ['pCube1.f[0]', 'pCube1.f[1]'])
		face_position (dict): Dictionary with keys 'u_min', 'u_max', 'v_min', 'v_max'
		uv_set (str): UV set name to work with. Empty string uses current UV set.
	"""
	# print(f"[normalize_uv_shell_position] face_list: {face_list}")
	# print(f"[normalize_uv_shell_position] face_position: {face_position}")
	# print(f"[normalize_uv_shell_position] uv_set: '{uv_set}'")

	# Group faces by mesh using API instead of string operations
	mesh_data = {}  # {dag_path_string: {'dag_path': MDagPath, 'face_indices': [int]}}

	for face_name in face_list:
		try:
			# Use MSelectionList to get proper DAG path and component
			temp_sel = om2.MSelectionList()
			temp_sel.add(face_name)
			dag_path, component = temp_sel.getComponent(0)

			# Get unique identifier for this mesh
			dag_path_str = dag_path.fullPathName()

			if dag_path_str not in mesh_data:
				mesh_data[dag_path_str] = {
					'dag_path': dag_path,
					'face_indices': []
				}

			# Extract face indices from component
			if component.hasFn(om2.MFn.kMeshPolygonComponent):
				face_it = om2.MItMeshPolygon(dag_path, component)
				while not face_it.isDone():
					mesh_data[dag_path_str]['face_indices'].append(face_it.index())
					face_it.next()
		except Exception as e:
			continue

	# Normalize face_position to [0,1] range
	face_u_min = face_position['u_min']
	face_u_max = face_position['u_max']
	face_v_min = face_position['v_min']
	face_v_max = face_position['v_max']

	# Normalize face_position coordinates to [0,1]
	def normalize_coordinate(coord):
		if coord < 0:
			return coord - int(coord) + 1
		elif coord > 1:
			return coord - int(coord)
		else:
			return coord

	norm_face_u_min = normalize_coordinate(face_u_min)
	norm_face_u_max = normalize_coordinate(face_u_max)
	norm_face_v_min = normalize_coordinate(face_v_min)
	norm_face_v_max = normalize_coordinate(face_v_max)

	for dag_path_str, mesh_info in mesh_data.items():
		try:
			dag_path = mesh_info['dag_path']
			face_indices = mesh_info['face_indices']
			mesh_fn = om2.MFnMesh(dag_path)

			# Step 1: Get all UV data once
			num_shells, shell_indices = mesh_fn.getUvShellsIds(uv_set)

			u_array, v_array = mesh_fn.getUVs(uv_set)

			# Step 1-1: Save uv position to attributes
			save_uvs_to_attribute(dag_path, u_array, v_array)

			# Step 2: Create structure for fast access to UV indices per shell
			shell_to_uvs = {}
			for uv_idx, shell_id in enumerate(shell_indices):
				if shell_id not in shell_to_uvs:
					shell_to_uvs[shell_id] = []
				shell_to_uvs[shell_id].append(uv_idx)

			# Step 3: Determine which shells need to be processed
			shells_to_process = set()
			for face_idx in face_indices:
				try:
					vertices_in_face = mesh_fn.polygonVertexCount(face_idx)
					for i in range(vertices_in_face):
						uv_idx = mesh_fn.getPolygonUVid(face_idx, i, uv_set)
						if uv_idx < len(shell_indices):  # Boundary check
							shells_to_process.add(shell_indices[uv_idx])
				except Exception as e:
					# print(f"[normalize_uv_shell_position] Error processing face_idx {face_idx}: {e}")
					traceback.print_exc()
					continue

			# Track if any changes were made
			changes_made = False

			# Step 4 & 5: Process each shell
			for shell_id in shells_to_process:
				if shell_id not in shell_to_uvs:
					continue

				shell_uv_indices = shell_to_uvs[shell_id]

				if not shell_uv_indices:
					continue

				# Calculate current shell bounds and pivot
				shell_u_coords = [u_array[i] for i in shell_uv_indices]
				shell_v_coords = [v_array[i] for i in shell_uv_indices]

				min_u = min(shell_u_coords)
				max_u = max(shell_u_coords)
				min_v = min(shell_v_coords)
				max_v = max(shell_v_coords)

				# Calculate current pivot (center of shell)
				pivot_u = (min_u + max_u) * 0.5
				pivot_v = (min_v + max_v) * 0.5

				# Step 1: Normalize shell pivot if it's outside [0,1]
				normal_shell_pivot_u = normalize_coordinate(pivot_u)
				normal_shell_pivot_v = normalize_coordinate(pivot_v)

				# Step 2: Convert normalized shell pivot back to [0,1] using face_position
				# Map normalized shell pivot to position within normalized face_position area
				face_width = norm_face_u_max - norm_face_u_min
				face_height = norm_face_v_max - norm_face_v_min

				# Calculate uniform scale factor based on larger face dimension
				max_face_dimension = max(face_width, face_height)
				scale_factor = max_face_dimension if max_face_dimension > 0 else 1.0 / 1.0

				# Use normalized shell pivot as relative position within face_position area
				renormal_pivot_u = norm_face_u_min + normal_shell_pivot_u * face_width
				renormal_pivot_v = norm_face_v_min + normal_shell_pivot_v * face_height

				# Apply uniform scaling relative to current pivot, then move to renormal_pivot
				for uv_idx in shell_uv_indices:
					# Scale relative to current pivot
					scaled_u = pivot_u + (u_array[uv_idx] - pivot_u) * scale_factor
					scaled_v = pivot_v + (v_array[uv_idx] - pivot_v) * scale_factor

					# Then translate to renormal_pivot
					u_array[uv_idx] = scaled_u + (renormal_pivot_u - pivot_u)
					v_array[uv_idx] = scaled_v + (renormal_pivot_v - pivot_v)

				changes_made = True

			# Step 6: Apply changes once if any were made
			if changes_made:
				mesh_fn.setUVs(u_array, v_array, uv_set)

		except Exception as e:
			traceback.print_exc()
			continue


def create_atlas_material_(mat_name, textures=None):
	if textures:
		albedo_texture = textures[0]
		normal_texture = textures[1]
	if cmds.objExists(mat_name):
		# If material exists, retrieve its connected shading group
		sg = cmds.listConnections(mat_name + ".outColor", d=True, s=False, type="shadingEngine")
		if not sg:
			cmds.warning("Material '{}' exists but has no shading group. Creating one.".format(mat_name))
			sg = cmds.sets(renderable=True, noSurfaceShader=True, empty=True, name=mat_name + "SG")
			cmds.connectAttr(mat_name + ".outColor", sg + ".surfaceShader", force=True)
		else:
			sg = sg[0]
		# Even if material exists, attach textures if provided
		if textures:
			# Process albedo texture: connect file node outColor to material color
			if os.path.exists(albedo_texture):
				file_albedo = cmds.shadingNode('file', asTexture=True, isColorManaged=True, name=mat_name + "_albedo")
				cmds.setAttr(file_albedo + ".fileTextureName", albedo_texture, type="string")
				cmds.connectAttr(file_albedo + ".outColor", mat_name + ".color", force=True)
			else:
				cmds.warning("Albedo texture file does not exist: " + albedo_texture)
			# Process normal texture: create file node and bump2d node, then connect accordingly
			if os.path.exists(normal_texture):
				file_normal = cmds.shadingNode('file', asTexture=True, isColorManaged=True, name=mat_name + "_normal")
				cmds.setAttr(file_normal + ".fileTextureName", normal_texture, type="string")
				bump_node = cmds.shadingNode('bump2d', asUtility=True, name=mat_name + "_bump")
				cmds.setAttr(bump_node + ".bumpInterp", 1)  # 1 for Tangent Space Normals
				cmds.connectAttr(file_normal + ".outAlpha", bump_node + ".bumpValue", force=True)
				cmds.connectAttr(bump_node + ".outNormal", mat_name + ".normalCamera", force=True)
			else:
				cmds.warning("Normal texture file does not exist: " + normal_texture)
		return sg

	try:
		# Create a blinn material
		material = cmds.shadingNode('blinn', asShader=True, name=mat_name)
		# Create a shading group and connect the material
		shading_group = cmds.sets(renderable=True, noSurfaceShader=True, empty=True, name=mat_name + "SG")
		cmds.connectAttr(material + ".outColor", shading_group + ".surfaceShader", force=True)
		# If textures are provided, attach them to the material
		if textures:
			# Process albedo texture: connect file node outColor to material color
			if os.path.exists(albedo_texture):
				file_albedo = cmds.shadingNode('file', asTexture=True, isColorManaged=True, name=mat_name + "_albedo")
				cmds.setAttr(file_albedo + ".fileTextureName", albedo_texture, type="string")
				cmds.connectAttr(file_albedo + ".outColor", material + ".color", force=True)
			else:
				cmds.warning("Albedo texture file does not exist: " + albedo_texture)
			# Process normal texture: create file node and bump2d node, then connect accordingly
			if os.path.exists(normal_texture):
				file_normal = cmds.shadingNode('file', asTexture=True, isColorManaged=True, name=mat_name + "_normal")
				cmds.setAttr(file_normal + ".fileTextureName", normal_texture, type="string")
				bump_node = cmds.shadingNode('bump2d', asUtility=True, name=mat_name + "_bump")
				cmds.setAttr(bump_node + ".bumpInterp", 1)  # 1 for Tangent Space Normals
				cmds.connectAttr(file_normal + ".outAlpha", bump_node + ".bumpValue", force=True)
				cmds.connectAttr(bump_node + ".outNormal", material + ".normalCamera", force=True)
			else:
				cmds.warning("Normal texture file does not exist: " + normal_texture)
		cmds.select(clear=True)
		return shading_group
	except Exception as e:
		cmds.error("Failed to create atlas material '{}': {}".format(mat_name, e))
		return None


def create_atlas_material(mat_name, textures=None):
	"""
	Creates or updates a material with optional albedo and normal textures.

	Args:
		mat_name (str): Name of the material to create/update
		textures (list, optional): [albedo_texture_path, normal_texture_path]

	Returns:
		str: Shading group name, or None if creation failed
	"""
	# Extract texture paths if provided
	albedo_texture = textures[0] if textures and len(textures) > 0 else None
	normal_texture = textures[1] if textures and len(textures) > 1 else None

	try:
		# Handle existing material
		if cmds.objExists(mat_name):
			sg = _get_or_create_shading_group(mat_name)
			material = mat_name
		else:
			# Create new material and shading group
			material = cmds.shadingNode('blinn', asShader=True, name=mat_name)
			sg = cmds.sets(renderable=True, noSurfaceShader=True, empty=True, name=mat_name + "SG")
			cmds.connectAttr(f"{material}.outColor", f"{sg}.surfaceShader", force=True)

		# Attach textures if provided
		if textures:
			_attach_textures(material, mat_name, albedo_texture, normal_texture)

		cmds.select(clear=True)
		return sg

	except Exception as e:
		cmds.error(f"Failed to create atlas material '{mat_name}': {e}")
		return None


def _get_or_create_shading_group(mat_name):
	"""Helper function to get existing shading group or create new one."""
	sg_list = cmds.listConnections(f"{mat_name}.outColor", d=True, s=False, type="shadingEngine")

	if sg_list:
		return sg_list[0]

	# Create shading group if none exists
	cmds.warning(f"Material '{mat_name}' exists but has no shading group. Creating one.")
	sg = cmds.sets(renderable=True, noSurfaceShader=True, empty=True, name=f"{mat_name}SG")
	cmds.connectAttr(f"{mat_name}.outColor", f"{sg}.surfaceShader", force=True)
	return sg


def _attach_textures(material, mat_name, albedo_texture, normal_texture):
	"""Helper function to attach albedo and normal textures to material."""
	# Process albedo texture
	if albedo_texture and os.path.exists(albedo_texture):
		file_albedo = cmds.shadingNode('file', asTexture=True, isColorManaged=True, name=f"{mat_name}_albedo")
		cmds.setAttr(f"{file_albedo}.fileTextureName", albedo_texture, type="string")
		cmds.connectAttr(f"{file_albedo}.outColor", f"{material}.color", force=True)
	elif albedo_texture:
		cmds.warning(f"Albedo texture file does not exist: {albedo_texture}")

	# Process normal texture
	if normal_texture and os.path.exists(normal_texture):
		file_normal = cmds.shadingNode('file', asTexture=True, isColorManaged=True, name=f"{mat_name}_normal")
		cmds.setAttr(f"{file_normal}.fileTextureName", normal_texture, type="string")

		bump_node = cmds.shadingNode('bump2d', asUtility=True, name=f"{mat_name}_bump")
		cmds.setAttr(f"{bump_node}.bumpInterp", 1)  # Tangent Space Normals

		cmds.connectAttr(f"{file_normal}.outAlpha", f"{bump_node}.bumpValue", force=True)
		cmds.connectAttr(f"{bump_node}.outNormal", f"{material}.normalCamera", force=True)
	elif normal_texture:
		cmds.warning(f"Normal texture file does not exist: {normal_texture}")


import maya.cmds as cmds
import maya.api.OpenMaya as om2


def save_uvs_to_attribute(mesh_shape_name, u_array=None, v_array=None):
	"""
	Сохраняет текущие UV-координаты меша в два пользовательских атрибута типа floatArray.

	Args:
		mesh_shape_name (str): Имя ноды формы меша.
		uv_set (str): Имя UV-сета для сохранения.
		attr_u (str): Имя атрибута для хранения U-координат.
		attr_v (str): Имя атрибута для хранения V-координат.
	"""

	attr_u = "customUvsU"
	attr_v = "customUvsV"

	# 2. Проверяем и создаем атрибуты, если их нет
	if not cmds.attributeQuery(attr_u, node=mesh_shape_name, exists=True):
		cmds.addAttr(mesh_shape_name, longName=attr_u, dataType="floatArray")
		print(f"Создан атрибут: {mesh_shape_name}.{attr_u}")

	if not cmds.attributeQuery(attr_v, node=mesh_shape_name, exists=True):
		cmds.addAttr(mesh_shape_name, longName=attr_v, dataType="floatArray")
		print(f"Создан атрибут: {mesh_shape_name}.{attr_v}")

	# 3. Записываем данные в атрибуты
	# Для setAttr с floatArray нужно передавать размер массива и затем распакованный список
	cmds.setAttr(f"{mesh_shape_name}.{attr_u}", u_array, type="floatArray")
	cmds.setAttr(f"{mesh_shape_name}.{attr_v}", v_array, type="floatArray")


def restore_uvs_from_attribute(mesh_shape_name):
	"""
	Восстанавливает UV-координаты меша из пользовательских атрибутов.

	Args:
		mesh_shape_name (str): Имя ноды формы меша.
		uv_set (str): Имя UV-сета для восстановления.
		attr_u (str): Имя атрибута для чтения U-координат.
		attr_v (str): Имя атрибута для чтения V-координат.
	"""
	attr_u = "customUvsU"
	attr_v = "customUvsV"
	uv_set = "map1"
	# 1. Проверяем наличие атрибутов
	if not cmds.attributeQuery(attr_u, node=mesh_shape_name, exists=True) or \
			not cmds.attributeQuery(attr_v, node=mesh_shape_name, exists=True):
		cmds.warning(f"Атрибуты для восстановления UV не найдены на '{mesh_shape_name}'.")
		return

	# 2. Считываем данные из атрибутов
	u_array = cmds.getAttr(f"{mesh_shape_name}.{attr_u}")
	v_array = cmds.getAttr(f"{mesh_shape_name}.{attr_v}")

	if not u_array or not v_array:
		cmds.warning(f"Атрибуты для восстановления UV на '{mesh_shape_name}' пусты.")
		return

	# 3. Применяем UV к мешу
	selection_list = om2.MSelectionList()
	selection_list.add(mesh_shape_name)
	dag_path = selection_list.getDagPath(0)
	mesh_fn = om2.MFnMesh(dag_path)

	# Применяем сохраненные UV
	mesh_fn.setUVs(u_array, v_array, uv_set)


def get_meshes_with_material(material_name):
	"""
	Returns a list of mesh names that have the specified material assigned (fully or partially).
	
	Args:
		material_name (str): The name of the material or shading engine to search for
		
	Returns:
		list: A list of mesh transform node names that use the specified material
	"""
	# Get the shading engine from material if needed
	shading_engine = material_name
	if not cmds.nodeType(material_name) == 'shadingEngine':
		# Check if it's a material node rather than a shading engine
		connections = cmds.listConnections(material_name + '.outColor',
		                                   type='shadingEngine',
		                                   d=True, s=False)
		if connections:
			shading_engine = connections[0]
		else:
			return []

	# Get all objects assigned to this shading engine
	try:
		# Create selection list for the shading engine
		sel_list = om2.MSelectionList()
		sel_list.add(shading_engine)
		sg_obj = sel_list.getDependNode(0)

		# Use MFnSet to get members of the shading engine
		fn_set = om2.MFnSet(sg_obj)
		members_sel_list = fn_set.getMembers(False)  # False for flatten=False

		# Store unique mesh transform names
		mesh_names = set()

		# Process each member
		for i in range(members_sel_list.length()):
			try:
				dag_path, component = members_sel_list.getComponent(i)

				# Skip if not a mesh
				if not dag_path.hasFn(om2.MFn.kMesh):
					continue

				# Add to our set of unique mesh names
				if cmds.objExists(dag_path.fullPathName()):
					mesh_names.add(dag_path.fullPathName())

			except Exception:
				continue

		return list(mesh_names)

	except Exception as e:
		cmds.warning(f"Error finding meshes with material '{material_name}': {e}")
		return []
